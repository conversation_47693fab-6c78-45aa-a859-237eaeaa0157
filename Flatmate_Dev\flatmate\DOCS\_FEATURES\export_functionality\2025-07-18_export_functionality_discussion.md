# Export Functionality: Discussion & Phase 1 Implementation Plan

## Context
Exporting data from the Categorise table view is a key workflow. Users expect to export exactly what they see (including filters/sorts) with minimal friction. This document outlines the requirements, design considerations, and a minimal implementation plan for phase 1 export functionality.

---

## Goals
- Allow users to export the current visible table (as shown, including filters/sorts) from the Categorise view.
- Use the existing export button in the table view toolbar.
- No need to track selection or changes—just snapshot/export the current table as seen.
- Support common formats (CSV at minimum; Excel optional).

---

## Design Principles
- **WYSIWYG Export:** Export matches exactly what is visible in the table (columns, order, filters, sorts).
- **Minimal Intrusion:** No new panels, modules, or UI for phase 1. Use existing controls.
- **Explicit, Readable Code:** Keep implementation simple and maintainable.
- **Extensible:** Lay groundwork for future expansion (dedicated export module/panel in later phases).

---

## Minimal Implementation Plan (Phase 1)

1. **Connect Export Button**
   - Ensure the existing export button calls a dedicated `export_current_view()` function.

2. **Snapshot Current Table**
   - Extract the current table view data (respecting filters/sorts) as displayed in the UI.
   - Convert to a `pandas.DataFrame` or equivalent structure for export.

3. **Prompt for File Destination/Format**
   - Use a file dialog to let the user choose filename and format (CSV required, Excel optional).

4. **Export Logic**
   - Use pandas or built-in methods to write the DataFrame to the chosen file.
   - Handle errors with clear, user-facing messages (no silent failures).

5. **Code Placement**
   - Place all logic in a single, well-named function (e.g., `export_current_view`).
   - Keep code explicit and well-commented for future maintainers.

---

## Future Considerations (Phase 2+)
- Dedicated export panel/module for advanced options (format, columns, metadata, batch export).
- User presets and export templates.
- Exporting raw, full, or filtered datasets.

---

---

## Technical Analysis & Design Considerations

### Current Implementation Status
- **Export Button**: Already exists in `ExportGroup` with CSV/Excel menu options
- **Signal Flow**: `ExportButton` → `ExportGroup` → `FMTableView` → `TableViewCore._export_data()`
- **Current Issue**: `get_dataframe()` returns original data, not filtered/sorted view

### Key Technical Insight
The current `_export_data()` method calls `self.get_dataframe()` which returns the original DataFrame from the model, **not** the filtered/sorted view that users see. This violates the WYSIWYG principle.

**Root Cause**: The table uses a proxy model (`EnhancedFilterProxyModel`) for filtering/sorting, but `get_dataframe()` bypasses it and returns raw data from the source model.

### Required Implementation Changes
1. **Create `get_visible_dataframe()` method** that extracts data as displayed (respecting proxy model state)
2. **Update `_export_data()` method** to use visible data instead of raw data
3. **Handle column visibility** - export only visible columns in their display order
4. **Preserve user edits** - include any cell edits made in the table view

### Architecture Benefits
- **Minimal Code Changes**: Only need to modify the data extraction method
- **Maintains Existing UI**: No changes to export button or user workflow
- **Future-Proof**: New method can be extended for advanced export features

### Error Handling Considerations
- **Empty Results**: Handle case where filters result in no visible rows
- **File Permissions**: Clear error messages for write permission issues
- **Large Datasets**: Consider progress indication for very large exports
- **Format Compatibility**: Ensure Excel export handles special characters/formatting

---

## Summary
This phase 1 plan delivers immediate, low-friction export functionality for users, matching current expectations and UI. The approach is simple, explicit, and easy to extend in future phases.

**Critical Fix**: The implementation must address the current technical gap where exports don't match the visible table view.
