# Preprocessing Layer Design for Package-Based Search

**Date:** 2025-07-18  
**Purpose:** Design architecture for normalizing user input to package syntax while maintaining backward compatibility

---

## Overview

The preprocessing layer serves as a bridge between user-friendly search syntax and the formal syntax expected by the chosen boolean search package (luqum). This layer ensures backward compatibility with existing Phase 1 syntax while enabling advanced features.

---

## Current Implementation Analysis

### Existing Pattern Parsing (Phase 1)
Located in: `enhanced_filter_proxy_model.py`

```python
def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
    """Parse pattern into AND terms and EXCLUDE terms."""
    # Current logic:
    # - Space-separated terms = AND
    # - Terms starting with '-' = EXCLUDE/NOT
    # - Simple, fast, works well
```

### Enhanced Pattern Parsing (Phase 2 - Partial)
```python
def _parse_filter_pattern_v2(self, pattern: str) -> dict:
    """Enhanced pattern parsing with OR and basic grouping support."""
    # Current logic:
    # - Handles | for OR
    # - Basic parentheses grouping
    # - Complex custom parsing logic
```

---

## Proposed Architecture

### 1. SearchQueryPreprocessor Class

```python
class SearchQueryPreprocessor:
    """Preprocesses user search queries for package-based parsing."""
    
    def __init__(self, target_syntax: str = "lucene"):
        """Initialize preprocessor for target syntax."""
        self.target_syntax = target_syntax
        self._operator_mappings = self._get_operator_mappings()
    
    def preprocess(self, user_query: str) -> str:
        """Convert user-friendly query to package syntax."""
        # 1. Normalize whitespace
        # 2. Handle operator synonyms
        # 3. Preserve quoted strings
        # 4. Convert to target syntax
        pass
    
    def _get_operator_mappings(self) -> dict:
        """Get operator mappings for target syntax."""
        if self.target_syntax == "lucene":
            return {
                # OR operators
                "|": " OR ",
                "/": " OR ",
                # NOT operators  
                "-": "NOT ",
                # AND operators (space) - handled implicitly by Lucene
            }
```

### 2. Integration with Current System

```python
class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with package-based parsing."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._column_filters = {}
        self._search_parser = SearchQueryParser()  # New package-based parser
        self._legacy_mode = False  # For fallback compatibility
    
    def _check_pattern_match(self, data_str: str, pattern: str) -> bool:
        """Check pattern match using package-based parser."""
        try:
            # Use new package-based approach
            return self._search_parser.evaluate(pattern, data_str)
        except Exception as e:
            # Fallback to legacy parsing if needed
            if not self._legacy_mode:
                print(f"Warning: Falling back to legacy parser: {e}")
                self._legacy_mode = True
            return self._legacy_check_pattern_match(data_str, pattern)
```

---

## Operator Synonym Mapping

### User Input → Lucene Syntax

| User Syntax | Lucene Equivalent | Description |
|-------------|-------------------|-------------|
| `coffee tea` | `coffee AND tea` | Implicit AND (space) |
| `coffee\|tea` | `coffee OR tea` | Pipe OR operator |
| `coffee/tea` | `coffee OR tea` | Slash OR operator |
| `-decaf` | `NOT decaf` | Dash NOT operator |
| `(coffee\|tea) -decaf` | `(coffee OR tea) AND NOT decaf` | Complex grouping |
| `"coffee shop"` | `"coffee shop"` | Quoted phrases (preserved) |

### Preprocessing Rules

1. **Preserve Quoted Strings**: Don't modify content within quotes
2. **Handle Operator Precedence**: Ensure correct grouping
3. **Normalize Whitespace**: Clean up extra spaces
4. **Validate Syntax**: Check for balanced parentheses
5. **Backward Compatibility**: Support all Phase 1 patterns

---

## Implementation Strategy

### Phase 1: Basic Preprocessing

```python
class BasicPreprocessor:
    """Basic preprocessing for operator synonym conversion."""
    
    def preprocess(self, query: str) -> str:
        """Convert basic operator synonyms."""
        # Handle quoted strings first
        quoted_parts = []
        query = self._extract_quoted_strings(query, quoted_parts)
        
        # Convert operators
        query = self._convert_or_operators(query)
        query = self._convert_not_operators(query)
        query = self._handle_implicit_and(query)
        
        # Restore quoted strings
        query = self._restore_quoted_strings(query, quoted_parts)
        
        return query
    
    def _extract_quoted_strings(self, query: str, storage: list) -> str:
        """Extract quoted strings to protect them from processing."""
        import re
        pattern = r'"([^"]*)"'
        
        def replace_quote(match):
            storage.append(match.group(0))
            return f"__QUOTE_{len(storage)-1}__"
        
        return re.sub(pattern, replace_quote, query)
    
    def _convert_or_operators(self, query: str) -> str:
        """Convert | and / to OR."""
        import re
        # Handle word|word and word/word patterns
        query = re.sub(r'(\w+)\|(\w+)', r'\1 OR \2', query)
        query = re.sub(r'(\w+)/(\w+)', r'\1 OR \2', query)
        return query
    
    def _convert_not_operators(self, query: str) -> str:
        """Convert -term to NOT term."""
        import re
        # Handle -word patterns (but not standalone -)
        query = re.sub(r'\s-(\w+)', r' NOT \1', query)
        query = re.sub(r'^-(\w+)', r'NOT \1', query)  # Handle start of string
        return query
    
    def _handle_implicit_and(self, query: str) -> str:
        """Handle implicit AND operators (spaces)."""
        # Lucene handles this naturally, but we might need explicit AND
        # for some cases. For now, let Lucene handle implicit AND.
        return query
    
    def _restore_quoted_strings(self, query: str, storage: list) -> str:
        """Restore quoted strings."""
        for i, quoted in enumerate(storage):
            query = query.replace(f"__QUOTE_{i}__", quoted)
        return query
```

### Phase 2: Advanced Preprocessing

```python
class AdvancedPreprocessor(BasicPreprocessor):
    """Advanced preprocessing with validation and optimization."""
    
    def preprocess(self, query: str) -> str:
        """Advanced preprocessing with validation."""
        # Basic preprocessing
        query = super().preprocess(query)
        
        # Advanced features
        query = self._validate_syntax(query)
        query = self._optimize_expression(query)
        
        return query
    
    def _validate_syntax(self, query: str) -> str:
        """Validate and fix common syntax issues."""
        # Check balanced parentheses
        if not self._check_balanced_parentheses(query):
            # Try to fix or raise warning
            query = self._fix_unbalanced_parentheses(query)
        
        # Check for empty groups
        query = self._remove_empty_groups(query)
        
        return query
    
    def _optimize_expression(self, query: str) -> str:
        """Optimize expression for better performance."""
        # Remove redundant operators
        query = self._remove_redundant_operators(query)
        
        # Simplify nested groups
        query = self._simplify_nested_groups(query)
        
        return query
```

---

## Integration Points

### 1. SearchQueryParser Wrapper

```python
class SearchQueryParser:
    """Main wrapper for package-based search parsing."""
    
    def __init__(self):
        self.preprocessor = AdvancedPreprocessor()
        self.parser = None  # Will be luqum parser
        self._initialize_parser()
    
    def _initialize_parser(self):
        """Initialize the chosen package parser."""
        try:
            from luqum import parser
            self.parser = parser
        except ImportError:
            raise ImportError("luqum package not installed")
    
    def parse(self, user_query: str):
        """Parse user query into structured expression."""
        # Preprocess user input
        normalized_query = self.preprocessor.preprocess(user_query)
        
        # Parse with package
        try:
            ast = self.parser.parse(normalized_query)
            return ast
        except Exception as e:
            raise ValueError(f"Failed to parse query '{user_query}': {e}")
    
    def evaluate(self, user_query: str, data_text: str) -> bool:
        """Evaluate query against data text."""
        ast = self.parse(user_query)
        return self._evaluate_ast(ast, data_text)
    
    def _evaluate_ast(self, ast, data_text: str) -> bool:
        """Evaluate parsed AST against data text."""
        # Custom visitor to evaluate AST against our data
        evaluator = SearchExpressionEvaluator(data_text.lower())
        return evaluator.visit(ast)
```

### 2. AST Evaluator

```python
class SearchExpressionEvaluator:
    """Evaluates parsed search expressions against data."""
    
    def __init__(self, data_text: str):
        self.data_text = data_text
    
    def visit(self, node):
        """Visit AST node and evaluate."""
        # Implement visitor pattern for different node types
        # This will depend on the specific AST structure from luqum
        pass
```

---

## Backward Compatibility Strategy

### 1. Gradual Migration
- Keep existing `_parse_filter_pattern` as fallback
- Introduce package-based parsing alongside
- Monitor performance and reliability
- Switch default after validation

### 2. Feature Flags
```python
class SearchConfig:
    USE_PACKAGE_PARSER = True
    FALLBACK_TO_LEGACY = True
    LOG_PARSING_ERRORS = True
```

### 3. Testing Strategy
- Comprehensive test suite covering all Phase 1 syntax
- Performance benchmarks
- Error handling validation
- User acceptance testing

---

## Performance Considerations

### 1. Caching
- Cache preprocessed queries for repeated patterns
- Cache parsed ASTs for complex expressions
- Monitor memory usage

### 2. Optimization
- Profile preprocessing overhead
- Optimize regex patterns
- Consider lazy evaluation

### 3. Fallback Strategy
- Quick detection of parsing failures
- Graceful degradation to legacy parser
- User notification of syntax issues

---

## Next Steps

1. **Implement BasicPreprocessor**: Start with simple operator conversion
2. **Create SearchQueryParser wrapper**: Basic integration with luqum
3. **Develop AST evaluator**: Custom visitor for our data evaluation
4. **Add comprehensive testing**: Ensure backward compatibility
5. **Performance validation**: Compare with current implementation
6. **User testing**: Validate syntax with real users

---

**This preprocessing layer design provides a clean separation between user syntax and package implementation, ensuring maintainability and extensibility while preserving backward compatibility.**
