Created by PLY version 3.11 (http://www.dabeaz.com/ply)

Grammar

Rule 0     S' -> expression
Rule 1     expression -> expression OR_OP expression
Rule 2     expression -> expression AND_OP expression
Rule 3     expression -> expression expression
Rule 4     unary_expression -> PLUS unary_expression
Rule 5     unary_expression -> MINUS unary_expression
Rule 6     unary_expression -> NOT unary_expression
Rule 7     expression -> unary_expression
Rule 8     unary_expression -> LPAREN expression RPAREN
Rule 9     unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
Rule 10    possibly_negative_term -> MINUS phrase_or_term
Rule 11    possibly_negative_term -> phrase_or_term
Rule 12    phrase_or_possibly_negative_term -> possibly_negative_term
Rule 13    phrase_or_possibly_negative_term -> PHRASE
Rule 14    unary_expression -> LESSTHAN phrase_or_term
Rule 15    unary_expression -> GREATERTHAN phrase_or_term
Rule 16    unary_expression -> TERM COLUMN unary_expression
Rule 17    unary_expression -> PHRASE
Rule 18    unary_expression -> PHRASE APPROX
Rule 19    unary_expression -> unary_expression BOOST
Rule 20    unary_expression -> TERM
Rule 21    unary_expression -> TERM APPROX
Rule 22    unary_expression -> REGEX
Rule 23    unary_expression -> TO
Rule 24    phrase_or_term -> TERM
Rule 25    phrase_or_term -> PHRASE

Terminals, with rules where they appear

AND_OP               : 2
APPROX               : 18 21
BOOST                : 19
COLUMN               : 16
GREATERTHAN          : 15
LBRACKET             : 9
LESSTHAN             : 14
LPAREN               : 8
MINUS                : 5 10
NOT                  : 6
OR_OP                : 1
PHRASE               : 13 17 18 25
PLUS                 : 4
RBRACKET             : 9
REGEX                : 22
RPAREN               : 8
TERM                 : 16 20 21 24
TO                   : 9 23
error                : 

Nonterminals, with rules where they appear

expression           : 1 1 2 2 3 3 8 0
phrase_or_possibly_negative_term : 9 9
phrase_or_term       : 10 11 14 15
possibly_negative_term : 12
unary_expression     : 4 5 6 7 16 19

Parsing method: LALR

state 0

    (0) S' -> . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 1
    unary_expression               shift and go to state 2

state 1

    (0) S' -> expression .
    (1) expression -> expression . OR_OP expression
    (2) expression -> expression . AND_OP expression
    (3) expression -> expression . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    OR_OP           shift and go to state 15
    AND_OP          shift and go to state 16
    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 14
    unary_expression               shift and go to state 2

state 2

    (7) expression -> unary_expression .
    (19) unary_expression -> unary_expression . BOOST

    OR_OP           reduce using rule 7 (expression -> unary_expression .)
    AND_OP          reduce using rule 7 (expression -> unary_expression .)
    PLUS            reduce using rule 7 (expression -> unary_expression .)
    MINUS           reduce using rule 7 (expression -> unary_expression .)
    NOT             reduce using rule 7 (expression -> unary_expression .)
    LPAREN          reduce using rule 7 (expression -> unary_expression .)
    LBRACKET        reduce using rule 7 (expression -> unary_expression .)
    LESSTHAN        reduce using rule 7 (expression -> unary_expression .)
    GREATERTHAN     reduce using rule 7 (expression -> unary_expression .)
    TERM            reduce using rule 7 (expression -> unary_expression .)
    PHRASE          reduce using rule 7 (expression -> unary_expression .)
    REGEX           reduce using rule 7 (expression -> unary_expression .)
    TO              reduce using rule 7 (expression -> unary_expression .)
    $end            reduce using rule 7 (expression -> unary_expression .)
    RPAREN          reduce using rule 7 (expression -> unary_expression .)
    BOOST           shift and go to state 17


state 3

    (4) unary_expression -> PLUS . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    unary_expression               shift and go to state 18

state 4

    (5) unary_expression -> MINUS . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    unary_expression               shift and go to state 19

state 5

    (6) unary_expression -> NOT . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    unary_expression               shift and go to state 20

state 6

    (8) unary_expression -> LPAREN . expression RPAREN
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 21
    unary_expression               shift and go to state 2

state 7

    (9) unary_expression -> LBRACKET . phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (12) phrase_or_possibly_negative_term -> . possibly_negative_term
    (13) phrase_or_possibly_negative_term -> . PHRASE
    (10) possibly_negative_term -> . MINUS phrase_or_term
    (11) possibly_negative_term -> . phrase_or_term
    (24) phrase_or_term -> . TERM
    (25) phrase_or_term -> . PHRASE

    PHRASE          shift and go to state 24
    MINUS           shift and go to state 25
    TERM            shift and go to state 27

    phrase_or_possibly_negative_term shift and go to state 22
    possibly_negative_term         shift and go to state 23
    phrase_or_term                 shift and go to state 26

state 8

    (23) unary_expression -> TO .

    BOOST           reduce using rule 23 (unary_expression -> TO .)
    OR_OP           reduce using rule 23 (unary_expression -> TO .)
    AND_OP          reduce using rule 23 (unary_expression -> TO .)
    PLUS            reduce using rule 23 (unary_expression -> TO .)
    MINUS           reduce using rule 23 (unary_expression -> TO .)
    NOT             reduce using rule 23 (unary_expression -> TO .)
    LPAREN          reduce using rule 23 (unary_expression -> TO .)
    LBRACKET        reduce using rule 23 (unary_expression -> TO .)
    LESSTHAN        reduce using rule 23 (unary_expression -> TO .)
    GREATERTHAN     reduce using rule 23 (unary_expression -> TO .)
    TERM            reduce using rule 23 (unary_expression -> TO .)
    PHRASE          reduce using rule 23 (unary_expression -> TO .)
    REGEX           reduce using rule 23 (unary_expression -> TO .)
    TO              reduce using rule 23 (unary_expression -> TO .)
    $end            reduce using rule 23 (unary_expression -> TO .)
    RPAREN          reduce using rule 23 (unary_expression -> TO .)


state 9

    (14) unary_expression -> LESSTHAN . phrase_or_term
    (24) phrase_or_term -> . TERM
    (25) phrase_or_term -> . PHRASE

    TERM            shift and go to state 27
    PHRASE          shift and go to state 29

    phrase_or_term                 shift and go to state 28

state 10

    (15) unary_expression -> GREATERTHAN . phrase_or_term
    (24) phrase_or_term -> . TERM
    (25) phrase_or_term -> . PHRASE

    TERM            shift and go to state 27
    PHRASE          shift and go to state 29

    phrase_or_term                 shift and go to state 30

state 11

    (16) unary_expression -> TERM . COLUMN unary_expression
    (20) unary_expression -> TERM .
    (21) unary_expression -> TERM . APPROX

    COLUMN          shift and go to state 31
    BOOST           reduce using rule 20 (unary_expression -> TERM .)
    OR_OP           reduce using rule 20 (unary_expression -> TERM .)
    AND_OP          reduce using rule 20 (unary_expression -> TERM .)
    PLUS            reduce using rule 20 (unary_expression -> TERM .)
    MINUS           reduce using rule 20 (unary_expression -> TERM .)
    NOT             reduce using rule 20 (unary_expression -> TERM .)
    LPAREN          reduce using rule 20 (unary_expression -> TERM .)
    LBRACKET        reduce using rule 20 (unary_expression -> TERM .)
    LESSTHAN        reduce using rule 20 (unary_expression -> TERM .)
    GREATERTHAN     reduce using rule 20 (unary_expression -> TERM .)
    TERM            reduce using rule 20 (unary_expression -> TERM .)
    PHRASE          reduce using rule 20 (unary_expression -> TERM .)
    REGEX           reduce using rule 20 (unary_expression -> TERM .)
    TO              reduce using rule 20 (unary_expression -> TERM .)
    $end            reduce using rule 20 (unary_expression -> TERM .)
    RPAREN          reduce using rule 20 (unary_expression -> TERM .)
    APPROX          shift and go to state 32


state 12

    (17) unary_expression -> PHRASE .
    (18) unary_expression -> PHRASE . APPROX

    BOOST           reduce using rule 17 (unary_expression -> PHRASE .)
    OR_OP           reduce using rule 17 (unary_expression -> PHRASE .)
    AND_OP          reduce using rule 17 (unary_expression -> PHRASE .)
    PLUS            reduce using rule 17 (unary_expression -> PHRASE .)
    MINUS           reduce using rule 17 (unary_expression -> PHRASE .)
    NOT             reduce using rule 17 (unary_expression -> PHRASE .)
    LPAREN          reduce using rule 17 (unary_expression -> PHRASE .)
    LBRACKET        reduce using rule 17 (unary_expression -> PHRASE .)
    LESSTHAN        reduce using rule 17 (unary_expression -> PHRASE .)
    GREATERTHAN     reduce using rule 17 (unary_expression -> PHRASE .)
    TERM            reduce using rule 17 (unary_expression -> PHRASE .)
    PHRASE          reduce using rule 17 (unary_expression -> PHRASE .)
    REGEX           reduce using rule 17 (unary_expression -> PHRASE .)
    TO              reduce using rule 17 (unary_expression -> PHRASE .)
    $end            reduce using rule 17 (unary_expression -> PHRASE .)
    RPAREN          reduce using rule 17 (unary_expression -> PHRASE .)
    APPROX          shift and go to state 33


state 13

    (22) unary_expression -> REGEX .

    BOOST           reduce using rule 22 (unary_expression -> REGEX .)
    OR_OP           reduce using rule 22 (unary_expression -> REGEX .)
    AND_OP          reduce using rule 22 (unary_expression -> REGEX .)
    PLUS            reduce using rule 22 (unary_expression -> REGEX .)
    MINUS           reduce using rule 22 (unary_expression -> REGEX .)
    NOT             reduce using rule 22 (unary_expression -> REGEX .)
    LPAREN          reduce using rule 22 (unary_expression -> REGEX .)
    LBRACKET        reduce using rule 22 (unary_expression -> REGEX .)
    LESSTHAN        reduce using rule 22 (unary_expression -> REGEX .)
    GREATERTHAN     reduce using rule 22 (unary_expression -> REGEX .)
    TERM            reduce using rule 22 (unary_expression -> REGEX .)
    PHRASE          reduce using rule 22 (unary_expression -> REGEX .)
    REGEX           reduce using rule 22 (unary_expression -> REGEX .)
    TO              reduce using rule 22 (unary_expression -> REGEX .)
    $end            reduce using rule 22 (unary_expression -> REGEX .)
    RPAREN          reduce using rule 22 (unary_expression -> REGEX .)


state 14

    (3) expression -> expression expression .
    (1) expression -> expression . OR_OP expression
    (2) expression -> expression . AND_OP expression
    (3) expression -> expression . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    NOT             reduce using rule 3 (expression -> expression expression .)
    LPAREN          reduce using rule 3 (expression -> expression expression .)
    LBRACKET        reduce using rule 3 (expression -> expression expression .)
    LESSTHAN        reduce using rule 3 (expression -> expression expression .)
    GREATERTHAN     reduce using rule 3 (expression -> expression expression .)
    TERM            reduce using rule 3 (expression -> expression expression .)
    PHRASE          reduce using rule 3 (expression -> expression expression .)
    REGEX           reduce using rule 3 (expression -> expression expression .)
    $end            reduce using rule 3 (expression -> expression expression .)
    RPAREN          reduce using rule 3 (expression -> expression expression .)
    OR_OP           shift and go to state 15
    AND_OP          shift and go to state 16
    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    TO              shift and go to state 8

  ! OR_OP           [ reduce using rule 3 (expression -> expression expression .) ]
  ! AND_OP          [ reduce using rule 3 (expression -> expression expression .) ]
  ! PLUS            [ reduce using rule 3 (expression -> expression expression .) ]
  ! MINUS           [ reduce using rule 3 (expression -> expression expression .) ]
  ! TO              [ reduce using rule 3 (expression -> expression expression .) ]
  ! NOT             [ shift and go to state 5 ]
  ! LPAREN          [ shift and go to state 6 ]
  ! LBRACKET        [ shift and go to state 7 ]
  ! LESSTHAN        [ shift and go to state 9 ]
  ! GREATERTHAN     [ shift and go to state 10 ]
  ! TERM            [ shift and go to state 11 ]
  ! PHRASE          [ shift and go to state 12 ]
  ! REGEX           [ shift and go to state 13 ]

    expression                     shift and go to state 14
    unary_expression               shift and go to state 2

state 15

    (1) expression -> expression OR_OP . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 34
    unary_expression               shift and go to state 2

state 16

    (2) expression -> expression AND_OP . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 35
    unary_expression               shift and go to state 2

state 17

    (19) unary_expression -> unary_expression BOOST .

    BOOST           reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    OR_OP           reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    AND_OP          reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    PLUS            reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    MINUS           reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    NOT             reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    LPAREN          reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    LBRACKET        reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    LESSTHAN        reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    GREATERTHAN     reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    TERM            reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    PHRASE          reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    REGEX           reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    TO              reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    $end            reduce using rule 19 (unary_expression -> unary_expression BOOST .)
    RPAREN          reduce using rule 19 (unary_expression -> unary_expression BOOST .)


state 18

    (4) unary_expression -> PLUS unary_expression .
    (19) unary_expression -> unary_expression . BOOST

    OR_OP           reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    AND_OP          reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    PLUS            reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    MINUS           reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    NOT             reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    LPAREN          reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    LBRACKET        reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    LESSTHAN        reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    GREATERTHAN     reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    TERM            reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    PHRASE          reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    REGEX           reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    TO              reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    $end            reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    RPAREN          reduce using rule 4 (unary_expression -> PLUS unary_expression .)
    BOOST           shift and go to state 17

  ! BOOST           [ reduce using rule 4 (unary_expression -> PLUS unary_expression .) ]


state 19

    (5) unary_expression -> MINUS unary_expression .
    (19) unary_expression -> unary_expression . BOOST

    OR_OP           reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    AND_OP          reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    PLUS            reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    MINUS           reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    NOT             reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    LPAREN          reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    LBRACKET        reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    LESSTHAN        reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    GREATERTHAN     reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    TERM            reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    PHRASE          reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    REGEX           reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    TO              reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    $end            reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    RPAREN          reduce using rule 5 (unary_expression -> MINUS unary_expression .)
    BOOST           shift and go to state 17

  ! BOOST           [ reduce using rule 5 (unary_expression -> MINUS unary_expression .) ]


state 20

    (6) unary_expression -> NOT unary_expression .
    (19) unary_expression -> unary_expression . BOOST

  ! shift/reduce conflict for BOOST resolved as shift
    OR_OP           reduce using rule 6 (unary_expression -> NOT unary_expression .)
    AND_OP          reduce using rule 6 (unary_expression -> NOT unary_expression .)
    PLUS            reduce using rule 6 (unary_expression -> NOT unary_expression .)
    MINUS           reduce using rule 6 (unary_expression -> NOT unary_expression .)
    NOT             reduce using rule 6 (unary_expression -> NOT unary_expression .)
    LPAREN          reduce using rule 6 (unary_expression -> NOT unary_expression .)
    LBRACKET        reduce using rule 6 (unary_expression -> NOT unary_expression .)
    LESSTHAN        reduce using rule 6 (unary_expression -> NOT unary_expression .)
    GREATERTHAN     reduce using rule 6 (unary_expression -> NOT unary_expression .)
    TERM            reduce using rule 6 (unary_expression -> NOT unary_expression .)
    PHRASE          reduce using rule 6 (unary_expression -> NOT unary_expression .)
    REGEX           reduce using rule 6 (unary_expression -> NOT unary_expression .)
    TO              reduce using rule 6 (unary_expression -> NOT unary_expression .)
    $end            reduce using rule 6 (unary_expression -> NOT unary_expression .)
    RPAREN          reduce using rule 6 (unary_expression -> NOT unary_expression .)
    BOOST           shift and go to state 17

  ! BOOST           [ reduce using rule 6 (unary_expression -> NOT unary_expression .) ]


state 21

    (8) unary_expression -> LPAREN expression . RPAREN
    (1) expression -> expression . OR_OP expression
    (2) expression -> expression . AND_OP expression
    (3) expression -> expression . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    RPAREN          shift and go to state 36
    OR_OP           shift and go to state 15
    AND_OP          shift and go to state 16
    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    expression                     shift and go to state 14
    unary_expression               shift and go to state 2

state 22

    (9) unary_expression -> LBRACKET phrase_or_possibly_negative_term . TO phrase_or_possibly_negative_term RBRACKET

    TO              shift and go to state 37


state 23

    (12) phrase_or_possibly_negative_term -> possibly_negative_term .

    TO              reduce using rule 12 (phrase_or_possibly_negative_term -> possibly_negative_term .)
    RBRACKET        reduce using rule 12 (phrase_or_possibly_negative_term -> possibly_negative_term .)


state 24

    (13) phrase_or_possibly_negative_term -> PHRASE .
    (25) phrase_or_term -> PHRASE .

  ! reduce/reduce conflict for TO resolved using rule 13 (phrase_or_possibly_negative_term -> PHRASE .)
  ! reduce/reduce conflict for RBRACKET resolved using rule 13 (phrase_or_possibly_negative_term -> PHRASE .)
    TO              reduce using rule 13 (phrase_or_possibly_negative_term -> PHRASE .)
    RBRACKET        reduce using rule 13 (phrase_or_possibly_negative_term -> PHRASE .)

  ! TO              [ reduce using rule 25 (phrase_or_term -> PHRASE .) ]
  ! RBRACKET        [ reduce using rule 25 (phrase_or_term -> PHRASE .) ]


state 25

    (10) possibly_negative_term -> MINUS . phrase_or_term
    (24) phrase_or_term -> . TERM
    (25) phrase_or_term -> . PHRASE

    TERM            shift and go to state 27
    PHRASE          shift and go to state 29

    phrase_or_term                 shift and go to state 38

state 26

    (11) possibly_negative_term -> phrase_or_term .

    TO              reduce using rule 11 (possibly_negative_term -> phrase_or_term .)
    RBRACKET        reduce using rule 11 (possibly_negative_term -> phrase_or_term .)


state 27

    (24) phrase_or_term -> TERM .

    TO              reduce using rule 24 (phrase_or_term -> TERM .)
    BOOST           reduce using rule 24 (phrase_or_term -> TERM .)
    OR_OP           reduce using rule 24 (phrase_or_term -> TERM .)
    AND_OP          reduce using rule 24 (phrase_or_term -> TERM .)
    PLUS            reduce using rule 24 (phrase_or_term -> TERM .)
    MINUS           reduce using rule 24 (phrase_or_term -> TERM .)
    NOT             reduce using rule 24 (phrase_or_term -> TERM .)
    LPAREN          reduce using rule 24 (phrase_or_term -> TERM .)
    LBRACKET        reduce using rule 24 (phrase_or_term -> TERM .)
    LESSTHAN        reduce using rule 24 (phrase_or_term -> TERM .)
    GREATERTHAN     reduce using rule 24 (phrase_or_term -> TERM .)
    TERM            reduce using rule 24 (phrase_or_term -> TERM .)
    PHRASE          reduce using rule 24 (phrase_or_term -> TERM .)
    REGEX           reduce using rule 24 (phrase_or_term -> TERM .)
    $end            reduce using rule 24 (phrase_or_term -> TERM .)
    RPAREN          reduce using rule 24 (phrase_or_term -> TERM .)
    RBRACKET        reduce using rule 24 (phrase_or_term -> TERM .)


state 28

    (14) unary_expression -> LESSTHAN phrase_or_term .

    BOOST           reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    OR_OP           reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    AND_OP          reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    PLUS            reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    MINUS           reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    NOT             reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    LPAREN          reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    LBRACKET        reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    LESSTHAN        reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    GREATERTHAN     reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    TERM            reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    PHRASE          reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    REGEX           reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    TO              reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    $end            reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)
    RPAREN          reduce using rule 14 (unary_expression -> LESSTHAN phrase_or_term .)


state 29

    (25) phrase_or_term -> PHRASE .

    BOOST           reduce using rule 25 (phrase_or_term -> PHRASE .)
    OR_OP           reduce using rule 25 (phrase_or_term -> PHRASE .)
    AND_OP          reduce using rule 25 (phrase_or_term -> PHRASE .)
    PLUS            reduce using rule 25 (phrase_or_term -> PHRASE .)
    MINUS           reduce using rule 25 (phrase_or_term -> PHRASE .)
    NOT             reduce using rule 25 (phrase_or_term -> PHRASE .)
    LPAREN          reduce using rule 25 (phrase_or_term -> PHRASE .)
    LBRACKET        reduce using rule 25 (phrase_or_term -> PHRASE .)
    LESSTHAN        reduce using rule 25 (phrase_or_term -> PHRASE .)
    GREATERTHAN     reduce using rule 25 (phrase_or_term -> PHRASE .)
    TERM            reduce using rule 25 (phrase_or_term -> PHRASE .)
    PHRASE          reduce using rule 25 (phrase_or_term -> PHRASE .)
    REGEX           reduce using rule 25 (phrase_or_term -> PHRASE .)
    TO              reduce using rule 25 (phrase_or_term -> PHRASE .)
    $end            reduce using rule 25 (phrase_or_term -> PHRASE .)
    RPAREN          reduce using rule 25 (phrase_or_term -> PHRASE .)
    RBRACKET        reduce using rule 25 (phrase_or_term -> PHRASE .)


state 30

    (15) unary_expression -> GREATERTHAN phrase_or_term .

    BOOST           reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    OR_OP           reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    AND_OP          reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    PLUS            reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    MINUS           reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    NOT             reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    LPAREN          reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    LBRACKET        reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    LESSTHAN        reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    GREATERTHAN     reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    TERM            reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    PHRASE          reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    REGEX           reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    TO              reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    $end            reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)
    RPAREN          reduce using rule 15 (unary_expression -> GREATERTHAN phrase_or_term .)


state 31

    (16) unary_expression -> TERM COLUMN . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    NOT             shift and go to state 5
    LPAREN          shift and go to state 6
    LBRACKET        shift and go to state 7
    LESSTHAN        shift and go to state 9
    GREATERTHAN     shift and go to state 10
    TERM            shift and go to state 11
    PHRASE          shift and go to state 12
    REGEX           shift and go to state 13
    TO              shift and go to state 8

    unary_expression               shift and go to state 39

state 32

    (21) unary_expression -> TERM APPROX .

    BOOST           reduce using rule 21 (unary_expression -> TERM APPROX .)
    OR_OP           reduce using rule 21 (unary_expression -> TERM APPROX .)
    AND_OP          reduce using rule 21 (unary_expression -> TERM APPROX .)
    PLUS            reduce using rule 21 (unary_expression -> TERM APPROX .)
    MINUS           reduce using rule 21 (unary_expression -> TERM APPROX .)
    NOT             reduce using rule 21 (unary_expression -> TERM APPROX .)
    LPAREN          reduce using rule 21 (unary_expression -> TERM APPROX .)
    LBRACKET        reduce using rule 21 (unary_expression -> TERM APPROX .)
    LESSTHAN        reduce using rule 21 (unary_expression -> TERM APPROX .)
    GREATERTHAN     reduce using rule 21 (unary_expression -> TERM APPROX .)
    TERM            reduce using rule 21 (unary_expression -> TERM APPROX .)
    PHRASE          reduce using rule 21 (unary_expression -> TERM APPROX .)
    REGEX           reduce using rule 21 (unary_expression -> TERM APPROX .)
    TO              reduce using rule 21 (unary_expression -> TERM APPROX .)
    $end            reduce using rule 21 (unary_expression -> TERM APPROX .)
    RPAREN          reduce using rule 21 (unary_expression -> TERM APPROX .)


state 33

    (18) unary_expression -> PHRASE APPROX .

    BOOST           reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    OR_OP           reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    AND_OP          reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    PLUS            reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    MINUS           reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    NOT             reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    LPAREN          reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    LBRACKET        reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    LESSTHAN        reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    GREATERTHAN     reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    TERM            reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    PHRASE          reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    REGEX           reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    TO              reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    $end            reduce using rule 18 (unary_expression -> PHRASE APPROX .)
    RPAREN          reduce using rule 18 (unary_expression -> PHRASE APPROX .)


state 34

    (1) expression -> expression OR_OP expression .
    (1) expression -> expression . OR_OP expression
    (2) expression -> expression . AND_OP expression
    (3) expression -> expression . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    OR_OP           reduce using rule 1 (expression -> expression OR_OP expression .)
    NOT             reduce using rule 1 (expression -> expression OR_OP expression .)
    LPAREN          reduce using rule 1 (expression -> expression OR_OP expression .)
    LBRACKET        reduce using rule 1 (expression -> expression OR_OP expression .)
    LESSTHAN        reduce using rule 1 (expression -> expression OR_OP expression .)
    GREATERTHAN     reduce using rule 1 (expression -> expression OR_OP expression .)
    TERM            reduce using rule 1 (expression -> expression OR_OP expression .)
    PHRASE          reduce using rule 1 (expression -> expression OR_OP expression .)
    REGEX           reduce using rule 1 (expression -> expression OR_OP expression .)
    $end            reduce using rule 1 (expression -> expression OR_OP expression .)
    RPAREN          reduce using rule 1 (expression -> expression OR_OP expression .)
    AND_OP          shift and go to state 16
    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    TO              shift and go to state 8

  ! AND_OP          [ reduce using rule 1 (expression -> expression OR_OP expression .) ]
  ! PLUS            [ reduce using rule 1 (expression -> expression OR_OP expression .) ]
  ! MINUS           [ reduce using rule 1 (expression -> expression OR_OP expression .) ]
  ! TO              [ reduce using rule 1 (expression -> expression OR_OP expression .) ]
  ! OR_OP           [ shift and go to state 15 ]
  ! NOT             [ shift and go to state 5 ]
  ! LPAREN          [ shift and go to state 6 ]
  ! LBRACKET        [ shift and go to state 7 ]
  ! LESSTHAN        [ shift and go to state 9 ]
  ! GREATERTHAN     [ shift and go to state 10 ]
  ! TERM            [ shift and go to state 11 ]
  ! PHRASE          [ shift and go to state 12 ]
  ! REGEX           [ shift and go to state 13 ]

    expression                     shift and go to state 14
    unary_expression               shift and go to state 2

state 35

    (2) expression -> expression AND_OP expression .
    (1) expression -> expression . OR_OP expression
    (2) expression -> expression . AND_OP expression
    (3) expression -> expression . expression
    (1) expression -> . expression OR_OP expression
    (2) expression -> . expression AND_OP expression
    (3) expression -> . expression expression
    (7) expression -> . unary_expression
    (4) unary_expression -> . PLUS unary_expression
    (5) unary_expression -> . MINUS unary_expression
    (6) unary_expression -> . NOT unary_expression
    (8) unary_expression -> . LPAREN expression RPAREN
    (9) unary_expression -> . LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET
    (14) unary_expression -> . LESSTHAN phrase_or_term
    (15) unary_expression -> . GREATERTHAN phrase_or_term
    (16) unary_expression -> . TERM COLUMN unary_expression
    (17) unary_expression -> . PHRASE
    (18) unary_expression -> . PHRASE APPROX
    (19) unary_expression -> . unary_expression BOOST
    (20) unary_expression -> . TERM
    (21) unary_expression -> . TERM APPROX
    (22) unary_expression -> . REGEX
    (23) unary_expression -> . TO

    OR_OP           reduce using rule 2 (expression -> expression AND_OP expression .)
    AND_OP          reduce using rule 2 (expression -> expression AND_OP expression .)
    NOT             reduce using rule 2 (expression -> expression AND_OP expression .)
    LPAREN          reduce using rule 2 (expression -> expression AND_OP expression .)
    LBRACKET        reduce using rule 2 (expression -> expression AND_OP expression .)
    LESSTHAN        reduce using rule 2 (expression -> expression AND_OP expression .)
    GREATERTHAN     reduce using rule 2 (expression -> expression AND_OP expression .)
    TERM            reduce using rule 2 (expression -> expression AND_OP expression .)
    PHRASE          reduce using rule 2 (expression -> expression AND_OP expression .)
    REGEX           reduce using rule 2 (expression -> expression AND_OP expression .)
    $end            reduce using rule 2 (expression -> expression AND_OP expression .)
    RPAREN          reduce using rule 2 (expression -> expression AND_OP expression .)
    PLUS            shift and go to state 3
    MINUS           shift and go to state 4
    TO              shift and go to state 8

  ! PLUS            [ reduce using rule 2 (expression -> expression AND_OP expression .) ]
  ! MINUS           [ reduce using rule 2 (expression -> expression AND_OP expression .) ]
  ! TO              [ reduce using rule 2 (expression -> expression AND_OP expression .) ]
  ! OR_OP           [ shift and go to state 15 ]
  ! AND_OP          [ shift and go to state 16 ]
  ! NOT             [ shift and go to state 5 ]
  ! LPAREN          [ shift and go to state 6 ]
  ! LBRACKET        [ shift and go to state 7 ]
  ! LESSTHAN        [ shift and go to state 9 ]
  ! GREATERTHAN     [ shift and go to state 10 ]
  ! TERM            [ shift and go to state 11 ]
  ! PHRASE          [ shift and go to state 12 ]
  ! REGEX           [ shift and go to state 13 ]

    expression                     shift and go to state 14
    unary_expression               shift and go to state 2

state 36

    (8) unary_expression -> LPAREN expression RPAREN .

    BOOST           reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    OR_OP           reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    AND_OP          reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    PLUS            reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    MINUS           reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    NOT             reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    LPAREN          reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    LBRACKET        reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    LESSTHAN        reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    GREATERTHAN     reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    TERM            reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    PHRASE          reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    REGEX           reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    TO              reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    $end            reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)
    RPAREN          reduce using rule 8 (unary_expression -> LPAREN expression RPAREN .)


state 37

    (9) unary_expression -> LBRACKET phrase_or_possibly_negative_term TO . phrase_or_possibly_negative_term RBRACKET
    (12) phrase_or_possibly_negative_term -> . possibly_negative_term
    (13) phrase_or_possibly_negative_term -> . PHRASE
    (10) possibly_negative_term -> . MINUS phrase_or_term
    (11) possibly_negative_term -> . phrase_or_term
    (24) phrase_or_term -> . TERM
    (25) phrase_or_term -> . PHRASE

    PHRASE          shift and go to state 24
    MINUS           shift and go to state 25
    TERM            shift and go to state 27

    phrase_or_possibly_negative_term shift and go to state 40
    possibly_negative_term         shift and go to state 23
    phrase_or_term                 shift and go to state 26

state 38

    (10) possibly_negative_term -> MINUS phrase_or_term .

    TO              reduce using rule 10 (possibly_negative_term -> MINUS phrase_or_term .)
    RBRACKET        reduce using rule 10 (possibly_negative_term -> MINUS phrase_or_term .)


state 39

    (16) unary_expression -> TERM COLUMN unary_expression .
    (19) unary_expression -> unary_expression . BOOST

  ! shift/reduce conflict for BOOST resolved as shift
    OR_OP           reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    AND_OP          reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    PLUS            reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    MINUS           reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    NOT             reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    LPAREN          reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    LBRACKET        reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    LESSTHAN        reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    GREATERTHAN     reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    TERM            reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    PHRASE          reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    REGEX           reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    TO              reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    $end            reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    RPAREN          reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .)
    BOOST           shift and go to state 17

  ! BOOST           [ reduce using rule 16 (unary_expression -> TERM COLUMN unary_expression .) ]


state 40

    (9) unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term . RBRACKET

    RBRACKET        shift and go to state 41


state 41

    (9) unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .

    BOOST           reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    OR_OP           reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    AND_OP          reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    PLUS            reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    MINUS           reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    NOT             reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    LPAREN          reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    LBRACKET        reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    LESSTHAN        reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    GREATERTHAN     reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    TERM            reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    PHRASE          reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    REGEX           reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    TO              reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    $end            reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)
    RPAREN          reduce using rule 9 (unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET .)

WARNING: 
WARNING: Conflicts:
WARNING: 
WARNING: shift/reduce conflict for BOOST in state 20 resolved as shift
WARNING: shift/reduce conflict for BOOST in state 39 resolved as shift
WARNING: reduce/reduce conflict in state 24 resolved using rule (phrase_or_possibly_negative_term -> PHRASE)
WARNING: rejected rule (phrase_or_term -> PHRASE) in state 24
