"""
Search Query Parser - Package-based boolean search implementation.

This module provides a wrapper around the luqum package for parsing boolean search
expressions with preprocessing to handle user-friendly operator synonyms.
"""

import re
import logging
from typing import Optional, List, Tuple, Any
from functools import lru_cache

try:
    from luqum import parser as luqum_parser
    from luqum.tree import SearchField, Word, Phrase, Group, AndOperation, OrOperation, Not
    LUQUM_AVAILABLE = True
except ImportError as e:
    LUQUM_AVAILABLE = False
    luqum_parser = None
    logger.debug(f"luqum import failed: {e}")

logger = logging.getLogger(__name__)


class SearchQueryPreprocessor:
    """Preprocesses user search queries for package-based parsing."""
    
    def __init__(self):
        """Initialize the preprocessor."""
        self._quoted_placeholder_pattern = r'__QUOTE_(\d+)__'
    
    def preprocess(self, user_query: str) -> str:
        """Convert user-friendly query to Lucene syntax.
        
        Args:
            user_query: User input query string
            
        Returns:
            Normalized query string for luqum parsing
        """
        if not user_query or not user_query.strip():
            return ""
        
        # Store quoted strings to protect them from processing
        quoted_parts = []
        query = self._extract_quoted_strings(user_query, quoted_parts)
        
        # Convert operator synonyms
        query = self._convert_or_operators(query)
        query = self._convert_not_operators(query)
        query = self._normalize_whitespace(query)
        
        # Restore quoted strings
        query = self._restore_quoted_strings(query, quoted_parts)
        
        return query.strip()
    
    def _extract_quoted_strings(self, query: str, storage: List[str]) -> str:
        """Extract quoted strings to protect them from processing."""
        pattern = r'"([^"]*)"'
        
        def replace_quote(match):
            storage.append(match.group(0))
            return f"__QUOTE_{len(storage)-1}__"
        
        return re.sub(pattern, replace_quote, query)
    
    def _convert_or_operators(self, query: str) -> str:
        """Convert | and / to OR."""
        # Handle word|word and word/word patterns
        query = re.sub(r'(\w+)\|(\w+)', r'\1 OR \2', query)
        query = re.sub(r'(\w+)/(\w+)', r'\1 OR \2', query)
        
        # Handle more complex patterns like (word1|word2)
        query = re.sub(r'\|', ' OR ', query)
        query = re.sub(r'/', ' OR ', query)
        
        return query
    
    def _convert_not_operators(self, query: str) -> str:
        """Convert -term to NOT term."""
        # Handle -word patterns (but not standalone -)
        query = re.sub(r'\s-(\w+)', r' NOT \1', query)
        query = re.sub(r'^-(\w+)', r'NOT \1', query)  # Handle start of string
        return query
    
    def _normalize_whitespace(self, query: str) -> str:
        """Normalize whitespace and clean up the query."""
        # Replace multiple spaces with single space
        query = re.sub(r'\s+', ' ', query)
        
        # Clean up spaces around operators
        query = re.sub(r'\s*OR\s*', ' OR ', query)
        query = re.sub(r'\s*AND\s*', ' AND ', query)
        query = re.sub(r'\s*NOT\s*', ' NOT ', query)
        
        return query.strip()
    
    def _restore_quoted_strings(self, query: str, storage: List[str]) -> str:
        """Restore quoted strings."""
        for i, quoted in enumerate(storage):
            query = query.replace(f"__QUOTE_{i}__", quoted)
        return query


class SearchExpressionEvaluator:
    """Evaluates parsed search expressions against data text."""
    
    def __init__(self, data_text: str):
        """Initialize evaluator with data text.
        
        Args:
            data_text: Text to evaluate expressions against (should be lowercase)
        """
        self.data_text = data_text.lower()
    
    def evaluate(self, node) -> bool:
        """Evaluate AST node against data text.
        
        Args:
            node: luqum AST node
            
        Returns:
            True if expression matches data text
        """
        if isinstance(node, Word):
            return self._evaluate_word(node)
        elif isinstance(node, Phrase):
            return self._evaluate_phrase(node)
        elif isinstance(node, AndOperation):
            return self._evaluate_and(node)
        elif isinstance(node, OrOperation):
            return self._evaluate_or(node)
        elif isinstance(node, Not):
            return self._evaluate_not(node)
        elif isinstance(node, Group):
            return self.evaluate(node.children[0])
        else:
            # For unknown node types, try to evaluate children
            if hasattr(node, 'children') and node.children:
                # Default to AND behavior for unknown operations
                return all(self.evaluate(child) for child in node.children)
            return True
    
    def _evaluate_word(self, node) -> bool:
        """Evaluate a word node."""
        word = str(node.value).lower()
        return word in self.data_text
    
    def _evaluate_phrase(self, node) -> bool:
        """Evaluate a phrase node (quoted string)."""
        phrase = str(node.value).lower().strip('"')
        return phrase in self.data_text
    
    def _evaluate_and(self, node) -> bool:
        """Evaluate an AND operation."""
        return all(self.evaluate(child) for child in node.children)
    
    def _evaluate_or(self, node) -> bool:
        """Evaluate an OR operation."""
        return any(self.evaluate(child) for child in node.children)
    
    def _evaluate_not(self, node) -> bool:
        """Evaluate a NOT operation."""
        # NOT should have exactly one child
        if node.children:
            return not self.evaluate(node.children[0])
        return True


class SearchQueryParser:
    """Main wrapper for package-based search parsing."""
    
    def __init__(self):
        """Initialize the search query parser."""
        self.preprocessor = SearchQueryPreprocessor()
        self._parser_available = LUQUM_AVAILABLE

        if not self._parser_available:
            logger.warning("luqum package not available, search functionality will be limited")

    @lru_cache(maxsize=128)
    def _cached_parse(self, normalized_query: str):
        """Cache parsed queries for better performance.

        Args:
            normalized_query: Preprocessed query string

        Returns:
            Parsed AST from luqum
        """
        if not self._parser_available:
            return None

        try:
            return luqum_parser.parse(normalized_query)
        except Exception as e:
            raise ValueError(f"Failed to parse normalized query '{normalized_query}': {e}")
    
    def is_available(self) -> bool:
        """Check if package-based parsing is available."""
        return self._parser_available
    
    def parse(self, user_query: str):
        """Parse user query into structured expression.
        
        Args:
            user_query: User input query string
            
        Returns:
            Parsed AST from luqum
            
        Raises:
            ValueError: If parsing fails
            RuntimeError: If luqum is not available
        """
        if not self._parser_available:
            raise RuntimeError("luqum package not available")
        
        if not user_query or not user_query.strip():
            return None
        
        # Preprocess user input
        normalized_query = self.preprocessor.preprocess(user_query)
        
        if not normalized_query:
            return None
        
        # Parse with luqum (cached)
        try:
            ast = self._cached_parse(normalized_query)
            return ast
        except Exception as e:
            raise ValueError(f"Failed to parse query '{user_query}': {e}")
    
    def evaluate(self, user_query: str, data_text: str) -> bool:
        """Evaluate query against data text.
        
        Args:
            user_query: User input query string
            data_text: Text to evaluate against
            
        Returns:
            True if query matches data text
        """
        if not self._parser_available:
            # Fallback to simple substring matching
            return self._fallback_evaluate(user_query, data_text)
        
        try:
            ast = self.parse(user_query)
            if ast is None:
                return True  # Empty query matches everything
            
            evaluator = SearchExpressionEvaluator(data_text)
            return evaluator.evaluate(ast)
        
        except Exception as e:
            logger.warning(f"Package parsing failed for '{user_query}': {e}, falling back to simple matching")
            return self._fallback_evaluate(user_query, data_text)
    
    def _fallback_evaluate(self, user_query: str, data_text: str) -> bool:
        """Fallback evaluation using simple string matching.
        
        This provides basic functionality when luqum is not available.
        """
        if not user_query or not user_query.strip():
            return True
        
        query_lower = user_query.lower()
        data_lower = data_text.lower()
        
        # Very basic AND logic (space-separated terms)
        terms = query_lower.split()
        for term in terms:
            if term.startswith('-') and len(term) > 1:
                # Exclude term
                if term[1:] in data_lower:
                    return False
            else:
                # Include term
                if term not in data_lower:
                    return False
        
        return True


# Singleton instance for global use
_search_parser_instance = None

def get_search_parser() -> SearchQueryParser:
    """Get the global search parser instance."""
    global _search_parser_instance
    if _search_parser_instance is None:
        _search_parser_instance = SearchQueryParser()
    return _search_parser_instance
