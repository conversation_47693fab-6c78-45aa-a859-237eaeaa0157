Metadata-Version: 2.0
Name: ply
Version: 3.11
Summary: Python Lex & Yacc
Home-page: http://www.dabeaz.com/ply/
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Description-Content-Type: UNKNOWN
Platform: UNKNOWN
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 2


PLY is yet another implementation of lex and yacc for Python. Some notable
features include the fact that its implemented entirely in Python and it
uses LALR(1) parsing which is efficient and well suited for larger grammars.

PLY provides most of the standard lex/yacc features including support for empty 
productions, precedence rules, error recovery, and support for ambiguous grammars. 

PLY is extremely easy to use and provides very extensive error checking. 
It is compatible with both Python 2 and Python 3.


