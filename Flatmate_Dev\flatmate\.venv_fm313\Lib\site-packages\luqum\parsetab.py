
# parsetab.py
# This file is automatically generated. Do not edit.
# pylint: disable=W,C,R
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = 'leftIMPLICIT_OPleftOR_OPleftAND_OPnonassocPLUSMINUSnonassocBOOSTnonassocTOrightUMINUSAND_OP APPROX BOOST COLUMN GREATERTHAN LBRACKET LESSTHAN LPAREN MINUS NOT OR_OP PHRASE PLUS RBRACKET REGEX RPAREN TERM TOexpression : expression OR_OP expressionexpression : expression AND_OP expressionexpression : expression expression %prec IMPLICIT_OPunary_expression : PLUS unary_expressionunary_expression : MINUS unary_expressionunary_expression : NOT unary_expressionexpression : unary_expressionunary_expression : LPAREN expression RPARENunary_expression : LBRACKET                            phrase_or_possibly_negative_term                            TO phrase_or_possibly_negative_term                           RBRACKETpossibly_negative_term : MINUS phrase_or_term  %prec UMINUS\n| phrase_or_termphrase_or_possibly_negative_term : possibly_negative_term\n| PHRASEunary_expression : LESSTHAN phrase_or_termunary_expression : GREATERTHAN phrase_or_termunary_expression : TERM COLUMN unary_expressionunary_expression : PHRASEunary_expression : PHRASE APPROXunary_expression : unary_expression BOOSTunary_expression : TERMunary_expression : TERM APPROXunary_expression : REGEXunary_expression : TOphrase_or_term : TERM\n| PHRASE'
    
_lr_action_items = {'PLUS':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[3,3,-7,3,3,3,3,-23,-20,-17,-22,3,3,3,-19,-4,-5,-6,3,-24,-14,-25,-15,3,-21,-18,3,3,-8,-16,-9,]),'MINUS':([0,1,2,3,4,5,6,7,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,37,39,41,],[4,4,-7,4,4,4,4,25,-23,-20,-17,-22,4,4,4,-19,-4,-5,-6,4,-24,-14,-25,-15,4,-21,-18,4,4,-8,25,-16,-9,]),'NOT':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[5,5,-7,5,5,5,5,-23,-20,-17,-22,-3,5,5,-19,-4,-5,-6,5,-24,-14,-25,-15,5,-21,-18,-1,-2,-8,-16,-9,]),'LPAREN':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[6,6,-7,6,6,6,6,-23,-20,-17,-22,-3,6,6,-19,-4,-5,-6,6,-24,-14,-25,-15,6,-21,-18,-1,-2,-8,-16,-9,]),'LBRACKET':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[7,7,-7,7,7,7,7,-23,-20,-17,-22,-3,7,7,-19,-4,-5,-6,7,-24,-14,-25,-15,7,-21,-18,-1,-2,-8,-16,-9,]),'LESSTHAN':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[9,9,-7,9,9,9,9,-23,-20,-17,-22,-3,9,9,-19,-4,-5,-6,9,-24,-14,-25,-15,9,-21,-18,-1,-2,-8,-16,-9,]),'GREATERTHAN':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[10,10,-7,10,10,10,10,-23,-20,-17,-22,-3,10,10,-19,-4,-5,-6,10,-24,-14,-25,-15,10,-21,-18,-1,-2,-8,-16,-9,]),'TERM':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,27,28,29,30,31,32,33,34,35,36,37,39,41,],[11,11,-7,11,11,11,11,27,-23,27,27,-20,-17,-22,-3,11,11,-19,-4,-5,-6,11,27,-24,-14,-25,-15,11,-21,-18,-1,-2,-8,27,-16,-9,]),'PHRASE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,27,28,29,30,31,32,33,34,35,36,37,39,41,],[12,12,-7,12,12,12,12,24,-23,29,29,-20,-17,-22,-3,12,12,-19,-4,-5,-6,12,29,-24,-14,-25,-15,12,-21,-18,-1,-2,-8,24,-16,-9,]),'REGEX':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,27,28,29,30,31,32,33,34,35,36,39,41,],[13,13,-7,13,13,13,13,-23,-20,-17,-22,-3,13,13,-19,-4,-5,-6,13,-24,-14,-25,-15,13,-21,-18,-1,-2,-8,-16,-9,]),'TO':([0,1,2,3,4,5,6,8,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28,29,30,31,32,33,34,35,36,38,39,41,],[8,8,-7,8,8,8,8,-23,-20,-17,-22,8,8,8,-19,-4,-5,-6,8,37,-12,-13,-11,-24,-14,-25,-15,8,-21,-18,8,8,-8,-10,-16,-9,]),'$end':([1,2,8,11,12,13,14,17,18,19,20,27,28,29,30,32,33,34,35,36,39,41,],[0,-7,-23,-20,-17,-22,-3,-19,-4,-5,-6,-24,-14,-25,-15,-21,-18,-1,-2,-8,-16,-9,]),'OR_OP':([1,2,8,11,12,13,14,17,18,19,20,21,27,28,29,30,32,33,34,35,36,39,41,],[15,-7,-23,-20,-17,-22,15,-19,-4,-5,-6,15,-24,-14,-25,-15,-21,-18,-1,-2,-8,-16,-9,]),'AND_OP':([1,2,8,11,12,13,14,17,18,19,20,21,27,28,29,30,32,33,34,35,36,39,41,],[16,-7,-23,-20,-17,-22,16,-19,-4,-5,-6,16,-24,-14,-25,-15,-21,-18,16,-2,-8,-16,-9,]),'RPAREN':([2,8,11,12,13,14,17,18,19,20,21,27,28,29,30,32,33,34,35,36,39,41,],[-7,-23,-20,-17,-22,-3,-19,-4,-5,-6,36,-24,-14,-25,-15,-21,-18,-1,-2,-8,-16,-9,]),'BOOST':([2,8,11,12,13,17,18,19,20,27,28,29,30,32,33,36,39,41,],[17,-23,-20,-17,-22,-19,17,17,17,-24,-14,-25,-15,-21,-18,-8,17,-9,]),'COLUMN':([11,],[31,]),'APPROX':([11,12,],[32,33,]),'RBRACKET':([23,24,26,27,29,38,40,],[-12,-13,-11,-24,-25,-10,41,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'expression':([0,1,6,14,15,16,21,34,35,],[1,14,21,14,34,35,14,14,14,]),'unary_expression':([0,1,3,4,5,6,14,15,16,21,31,34,35,],[2,2,18,19,20,2,2,2,2,2,39,2,2,]),'phrase_or_possibly_negative_term':([7,37,],[22,40,]),'possibly_negative_term':([7,37,],[23,23,]),'phrase_or_term':([7,9,10,25,37,],[26,28,30,38,26,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> expression","S'",1,None,None,None),
  ('expression -> expression OR_OP expression','expression',3,'p_expression_or','parser.py',254),
  ('expression -> expression AND_OP expression','expression',3,'p_expression_and','parser.py',260),
  ('expression -> expression expression','expression',2,'p_expression_implicit','parser.py',266),
  ('unary_expression -> PLUS unary_expression','unary_expression',2,'p_expression_plus','parser.py',272),
  ('unary_expression -> MINUS unary_expression','unary_expression',2,'p_expression_minus','parser.py',278),
  ('unary_expression -> NOT unary_expression','unary_expression',2,'p_expression_not','parser.py',284),
  ('expression -> unary_expression','expression',1,'p_expression_unary','parser.py',290),
  ('unary_expression -> LPAREN expression RPAREN','unary_expression',3,'p_grouping','parser.py',295),
  ('unary_expression -> LBRACKET phrase_or_possibly_negative_term TO phrase_or_possibly_negative_term RBRACKET','unary_expression',5,'p_range','parser.py',301),
  ('possibly_negative_term -> MINUS phrase_or_term','possibly_negative_term',2,'p_possibly_negative_term','parser.py',313),
  ('possibly_negative_term -> phrase_or_term','possibly_negative_term',1,'p_possibly_negative_term','parser.py',314),
  ('phrase_or_possibly_negative_term -> possibly_negative_term','phrase_or_possibly_negative_term',1,'p_phrase_or_possibly_negative_term','parser.py',323),
  ('phrase_or_possibly_negative_term -> PHRASE','phrase_or_possibly_negative_term',1,'p_phrase_or_possibly_negative_term','parser.py',324),
  ('unary_expression -> LESSTHAN phrase_or_term','unary_expression',2,'p_lessthan','parser.py',329),
  ('unary_expression -> GREATERTHAN phrase_or_term','unary_expression',2,'p_greaterthan','parser.py',336),
  ('unary_expression -> TERM COLUMN unary_expression','unary_expression',3,'p_field_search','parser.py',343),
  ('unary_expression -> PHRASE','unary_expression',1,'p_quoting','parser.py',352),
  ('unary_expression -> PHRASE APPROX','unary_expression',2,'p_proximity','parser.py',357),
  ('unary_expression -> unary_expression BOOST','unary_expression',2,'p_boosting','parser.py',363),
  ('unary_expression -> TERM','unary_expression',1,'p_terms','parser.py',369),
  ('unary_expression -> TERM APPROX','unary_expression',2,'p_fuzzy','parser.py',374),
  ('unary_expression -> REGEX','unary_expression',1,'p_regex','parser.py',380),
  ('unary_expression -> TO','unary_expression',1,'p_to_as_term','parser.py',386),
  ('phrase_or_term -> TERM','phrase_or_term',1,'p_phrase_or_term','parser.py',392),
  ('phrase_or_term -> PHRASE','phrase_or_term',1,'p_phrase_or_term','parser.py',393),
]
